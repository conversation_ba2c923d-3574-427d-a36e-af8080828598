{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\indexs\\center-map.vue", "mtime": 1754029068757}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["center-map.vue"], "names": [], "mappings": ";AA0CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center-map.vue", "sourceRoot": "src/views/indexs", "sourcesContent": ["<!--\r\n * @Author: daidai\r\n * @Date: 2022-03-01 11:17:39\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2022-09-29 15:50:18\r\n * @FilePath: \\web-pc\\src\\pages\\big-screen\\view\\indexs\\center-map.vue\r\n-->\r\n<template>\r\n  <div class=\"centermap\">\r\n    <div class=\"maptitle\">\r\n      <div class=\"zuo\"></div>\r\n      <span class=\"titletext\">{{ maptitle }}</span>\r\n      <div class=\"you\"></div>\r\n    </div>\r\n    <div class=\"mapwrap\">\r\n      <dv-border-box-13>\r\n        <!-- 使用新的 BigMap 组件替换 ECharts 地图 -->\r\n        <BigMap\r\n          ref=\"bigMap\"\r\n          :height=\"'100%'\"\r\n          :center=\"mapCenter\"\r\n          :zoom=\"mapZoom\"\r\n          :current-layer=\"currentLayer\"\r\n          @map-ready=\"onMapReady\"\r\n          @map-click=\"onMapClick\"\r\n          class=\"big-map-container\"\r\n        >\r\n          <!-- 地图控件 -->\r\n          <MapControls\r\n            v-if=\"map\"\r\n            :map=\"map\"\r\n            :current-layer=\"currentLayer\"\r\n            @layer-change=\"onLayerChange\"\r\n            @fullscreen-change=\"onFullscreenChange\"\r\n          />\r\n        </BigMap>\r\n      </dv-border-box-13>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport xzqCode from \"../../utils/map/xzqCode\";\r\nimport { currentGET } from \"api/modules\";\r\nimport * as echarts from \"echarts\";\r\nimport { GETNOBASE } from \"api\";\r\nimport { dataModule } from '@/utils/webSocket';\r\nexport default {\r\n  data() {\r\n    return {\r\n      maptitle: \"企业数量： 家 | 接入船总数量： 艘\",\r\n      options: {},\r\n      code: \"china\", //china 代表中国 其他地市是行政编码\r\n      echartBindClick: false,\r\n      isSouthChinaSea: false, //是否要展示南海群岛  修改此值请刷新页面\r\n      wsCheckTimer: null, // WebSocket数据监听定时器\r\n      shipTotalCount: 0, // 船舶总数量\r\n      enterpriseTotalCount: 0, // 企业总数量\r\n    };\r\n  },\r\n  created() {},\r\n\r\n  mounted() {\r\n    // console.log(xzqCode);\r\n    this.getData(\"china\");\r\n    this.startDataMonitoring();\r\n  },\r\n  beforeDestroy() {\r\n    // 清除定时器\r\n    if (this.wsCheckTimer) {\r\n      clearInterval(this.wsCheckTimer);\r\n      this.wsCheckTimer = null;\r\n    }\r\n  },\r\n  methods: {\r\n    // 开始WebSocket数据监听\r\n    startDataMonitoring() {\r\n      this.wsCheckTimer = setInterval(() => {\r\n        this.checkWebSocketData();\r\n      }, 1000);\r\n    },\r\n\r\n    // 检查WebSocket数据\r\n    checkWebSocketData() {\r\n      if (dataModule.D0A02) {\r\n        const newData = dataModule.D0A02;\r\n        // 检查数据是否有变化\r\n        if (newData.ship_num !== this.shipTotalCount || newData.enterprise_num !== this.enterpriseTotalCount) {\r\n          this.shipTotalCount = newData.ship_num || 0;\r\n          this.enterpriseTotalCount = newData.enterprise_num || 0;\r\n\r\n          // 更新地图标题显示\r\n          this.updateMapTitle();\r\n\r\n          console.log('船舶数量数据更新:', {\r\n            ship_num: this.shipTotalCount,\r\n            enterprise_num: this.enterpriseTotalCount\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    // 更新地图标题\r\n    updateMapTitle() {\r\n      this.maptitle = `企业数量：${this.enterpriseTotalCount} 家 | 接入船总数量：${this.shipTotalCount} 艘`;\r\n    },\r\n\r\n    getData(code) {\r\n      currentGET(\"big8\", { regionCode: code }).then((res) => {\r\n        console.log(\"设备分布\", res);\r\n        if (res.success) {\r\n          this.getGeojson(res.data.regionCode, res.data.dataList);\r\n          this.mapclick();\r\n        } else {\r\n          this.$Message.warning(res.msg);\r\n        }\r\n      });\r\n    },\r\n    /**\r\n     * @description: 获取geojson\r\n     * @param {*} name china 表示中国 其他省份行政区编码\r\n     * @param {*} mydata 接口返回列表数据\r\n     * @return {*}\r\n     */\r\n    async getGeojson(name, mydata) {\r\n      this.code = name;\r\n      //如果要展示南海群岛并且展示的是中国的话\r\n      let geoname=name\r\n      if (this.isSouthChinaSea && name == \"china\") {\r\n        geoname = \"chinaNanhai\";\r\n      }\r\n      //如果有注册地图的话就不用再注册 了\r\n      let mapjson = echarts.getMap(name);\r\n      if (mapjson) {\r\n        mapjson = mapjson.geoJSON;\r\n      } else {\r\n        mapjson = await GETNOBASE(`./map-geojson/${geoname}.json`).then((res) => {\r\n          return res;\r\n        });\r\n        echarts.registerMap(name, mapjson);\r\n      }\r\n      let cityCenter = {};\r\n      let arr = mapjson.features;\r\n      //根据geojson获取省份中心点\r\n      arr.map((item) => {\r\n        cityCenter[item.properties.name] =\r\n          item.properties.centroid || item.properties.center;\r\n      });\r\n      let newData = [];\r\n      mydata.map((item) => {\r\n        if (cityCenter[item.name]) {\r\n          newData.push({\r\n            name: item.name,\r\n            value: cityCenter[item.name].concat(item.value),\r\n          });\r\n        }\r\n      });\r\n      this.init(name, mydata, newData);\r\n    },\r\n    init(name, data, data2) {\r\n      // console.log(data2);\r\n      let top = 45;\r\n      let zoom = 1.05;\r\n      let option = {\r\n        backgroundColor: \"rgba(0,0,0,0)\",\r\n        tooltip: {\r\n          show: false,\r\n        },\r\n        legend: {\r\n          show: false,\r\n        },\r\n        visualMap: {\r\n          left: 20,\r\n          bottom: 20,\r\n          pieces: [\r\n            { gte: 1000, label: \"1000个以上\" }, // 不指定 max，表示 max 为无限大（Infinity）。\r\n            { gte: 600, lte: 999, label: \"600-999个\" },\r\n            { gte: 200, lte: 599, label: \"200-599个\" },\r\n            { gte: 50, lte: 199, label: \"49-199个\" },\r\n            { gte: 10, lte: 49, label: \"10-49个\" },\r\n            { lte: 9, label: \"1-9个\" }, // 不指定 min，表示 min 为无限大（-Infinity）。\r\n          ],\r\n          inRange: {\r\n            // 渐变颜色，从小到大\r\n            color: [\r\n              \"#c3d7df\",\r\n              \"#5cb3cc\",\r\n              \"#8abcd1\",\r\n              \"#66a9c9\",\r\n              \"#2f90b9\",\r\n              \"#1781b5\",\r\n            ],\r\n          },\r\n          textStyle: {\r\n            color: \"#fff\",\r\n          },\r\n        },\r\n        geo: {\r\n          map: name,\r\n          roam: false,\r\n          selectedMode: false, //是否允许选中多个区域\r\n          zoom: zoom,\r\n          top: top,\r\n          // aspectScale: 0.78,\r\n          show: false,\r\n        },\r\n        series: [\r\n          {\r\n            name: \"MAP\",\r\n            type: \"map\",\r\n            map: name,\r\n            // aspectScale: 0.78,\r\n            data: data,\r\n            // data: [1,100],\r\n            selectedMode: false, //是否允许选中多个区域\r\n            zoom: zoom,\r\n            geoIndex: 1,\r\n            top: top,\r\n            tooltip: {\r\n              show: true,\r\n              formatter: function (params) {\r\n                if (params.data) {\r\n                  return params.name + \"：\" + params.data[\"value\"];\r\n                } else {\r\n                  return params.name;\r\n                }\r\n              },\r\n              backgroundColor: \"rgba(0,0,0,.6)\",\r\n              borderColor: \"rgba(147, 235, 248, .8)\",\r\n              textStyle: {\r\n                color: \"#FFF\",\r\n              },\r\n            },\r\n            label: {\r\n              show: false,\r\n              color: \"#000\",\r\n              // position: [-10, 0],\r\n              formatter: function (val) {\r\n                // console.log(val)\r\n                if (val.data !== undefined) {\r\n                  return val.name.slice(0, 2);\r\n                } else {\r\n                  return \"\";\r\n                }\r\n              },\r\n              rich: {},\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: false,\r\n              },\r\n              itemStyle: {\r\n                areaColor: \"#389BB7\",\r\n                borderWidth: 1,\r\n              },\r\n            },\r\n            itemStyle: {\r\n              borderColor: \"rgba(147, 235, 248, .8)\",\r\n              borderWidth: 1,\r\n              areaColor: {\r\n                type: \"radial\",\r\n                x: 0.5,\r\n                y: 0.5,\r\n                r: 0.8,\r\n                colorStops: [\r\n                  {\r\n                    offset: 0,\r\n                    color: \"rgba(147, 235, 248, 0)\", // 0% 处的颜色\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: \"rgba(147, 235, 248, .2)\", // 100% 处的颜色\r\n                  },\r\n                ],\r\n                globalCoord: false, // 缺为 false\r\n              },\r\n              shadowColor: \"rgba(128, 217, 248, .3)\",\r\n              shadowOffsetX: -2,\r\n              shadowOffsetY: 2,\r\n              shadowBlur: 10,\r\n            },\r\n          },\r\n          {\r\n            data: data2,\r\n            type: \"effectScatter\",\r\n            coordinateSystem: \"geo\",\r\n            symbolSize: function (val) {\r\n              return 4;\r\n              // return val[2] / 50;\r\n            },\r\n            legendHoverLink: true,\r\n            showEffectOn: \"render\",\r\n            rippleEffect: {\r\n              // period: 4,\r\n              scale: 6,\r\n              color: \"rgba(255,255,255, 1)\",\r\n              brushType: \"fill\",\r\n            },\r\n            tooltip: {\r\n              show: true,\r\n              formatter: function (params) {\r\n                if (params.data) {\r\n                  return params.name + \"：\" + params.data[\"value\"][2];\r\n                } else {\r\n                  return params.name;\r\n                }\r\n              },\r\n              backgroundColor: \"rgba(0,0,0,.6)\",\r\n              borderColor: \"rgba(147, 235, 248, .8)\",\r\n              textStyle: {\r\n                color: \"#FFF\",\r\n              },\r\n            },\r\n            label: {\r\n              formatter: (param) => {\r\n                return param.name.slice(0, 2);\r\n              },\r\n\r\n              fontSize: 11,\r\n              offset: [0, 2],\r\n              position: \"bottom\",\r\n              textBorderColor: \"#fff\",\r\n              textShadowColor: \"#000\",\r\n              textShadowBlur: 10,\r\n              textBorderWidth: 0,\r\n              color: \"#FFF\",\r\n              show: true,\r\n            },\r\n            // colorBy: \"data\",\r\n            itemStyle: {\r\n              color: \"rgba(255,255,255,1)\",\r\n              borderColor: \"rgba(2255,255,255,2)\",\r\n              borderWidth: 4,\r\n              shadowColor: \"#000\",\r\n              shadowBlur: 10,\r\n            },\r\n          },\r\n        ],\r\n         //动画效果\r\n            // animationDuration: 1000,\r\n            // animationEasing: 'linear',\r\n            // animationDurationUpdate: 1000\r\n      };\r\n      this.options = option;\r\n    },\r\n    message(text) {\r\n      this.$Message({\r\n        text: text,\r\n        type: \"warning\",\r\n      });\r\n    },\r\n    mapclick() {\r\n      if (this.echartBindClick) return;\r\n      //单击切换到级地图，当mapCode有值,说明可以切换到下级地图\r\n      this.$refs.CenterMap.chart.on(\"click\", (params) => {\r\n        // console.log(params);\r\n        let xzqData = xzqCode[params.name];\r\n        if (xzqData) {\r\n          this.getData(xzqData.adcode);\r\n        } else {\r\n          this.message(\"暂无下级地市!\");\r\n        }\r\n      });\r\n      this.echartBindClick = true;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.centermap {\r\n  .maptitle {\r\n    display: flex;\r\n    justify-content: right;\r\n    margin: 25px 0 5px 0;\r\n    box-sizing: border-box;\r\n\r\n    .titletext {\r\n      font-size: 26px;\r\n      font-weight: 300;\r\n      letter-spacing: 1px;\r\n      background: linear-gradient(\r\n        92deg,\r\n        #0072ff 0%,\r\n        #00eaff 48.8525390625%,\r\n        #01aaff 100%\r\n      );\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      margin: 0 16px;\r\n    }\r\n\r\n    .zuo,\r\n    .you {\r\n      background-size: 100% 100%;\r\n      width: 26px;\r\n      height: 16px;\r\n      margin-top: 7px;\r\n    }\r\n\r\n    .zuo {\r\n      background: url(\"../../assets/img/xiezuo.png\") no-repeat;\r\n    }\r\n\r\n    .you {\r\n      background: url(\"../../assets/img/xieyou.png\") no-repeat;\r\n    }\r\n  }\r\n\r\n  .mapwrap {\r\n    height: 900px;\r\n    width: 100%;\r\n    // padding: 0 0 10px 0;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n\r\n    .quanguo {\r\n      position: absolute;\r\n      right: 20px;\r\n      top: -46px;\r\n      width: 80px;\r\n      height: 28px;\r\n      border: 1px solid #00eded;\r\n      border-radius: 10px;\r\n      color: #00f7f6;\r\n      text-align: center;\r\n      line-height: 26px;\r\n      letter-spacing: 6px;\r\n      cursor: pointer;\r\n      box-shadow: 0 2px 4px rgba(0, 237, 237, 0.5),\r\n        0 0 6px rgba(0, 237, 237, 0.4);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}