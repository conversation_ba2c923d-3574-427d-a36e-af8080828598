{"remainingRequest": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--5!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue?vue&type=template&id=23b0f17b&scoped=true&", "dependencies": [{"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\src\\views\\comindexs\\index.vue", "mtime": 1754030373987}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753075298398}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753075295560}, {"path": "D:\\_Workspaces\\_Active\\dct3\\code\\WEB\\snct-screen\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753075297856}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}