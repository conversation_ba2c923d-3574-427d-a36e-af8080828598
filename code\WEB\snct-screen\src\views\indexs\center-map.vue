<!--
 * @Author: daidai
 * @Date: 2022-03-01 11:17:39
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-09-29 15:50:18
 * @FilePath: \web-pc\src\pages\big-screen\view\indexs\center-map.vue
-->
<template>
  <div class="centermap">
    <div class="maptitle">
      <div class="zuo"></div>
      <span class="titletext">{{ maptitle }}</span>
      <div class="you"></div>
    </div>
    <div class="mapwrap">
      <dv-border-box-13>
        <!-- 使用新的 BigMap 组件替换 ECharts 地图 -->
        <BigMap
          ref="bigMap"
          :height="'100%'"
          :center="mapCenter"
          :zoom="mapZoom"
          :current-layer="currentLayer"
          @map-ready="onMapReady"
          @map-click="onMapClick"
          class="big-map-container"
        >
          <!-- 地图控件 -->
          <MapControls
            v-if="map"
            :map="map"
            :current-layer="currentLayer"
            @layer-change="onLayerChange"
            @fullscreen-change="onFullscreenChange"
          />
        </BigMap>
      </dv-border-box-13>
    </div>
  </div>
</template>

<script>
import BigMap from '@/components/map/BigMap.vue'
import MapControls from '@/components/map/MapControls.vue'
import { currentGET } from "api/modules";
import { dataModule } from '@/utils/webSocket';
import { createNineLineLayer } from '@/utils/map/nineLineData'

export default {
  components: {
    BigMap,
    MapControls
  },
  data() {
    return {
      maptitle: "企业数量： 家 | 接入船总数量： 艘",
      map: null, // 地图实例
      mapCenter: [120.0, 30.0], // 地图中心点
      mapZoom: 5, // 地图缩放级别
      currentLayer: 'satellite', // 当前图层
      code: "china", // 当前区域代码
      wsCheckTimer: null, // WebSocket数据监听定时器
      shipTotalCount: 0, // 船舶总数量
      enterpriseTotalCount: 0, // 企业总数量
      enterpriseMarkers: [], // 企业标记数组
      nineLineOverlays: [], // 九段线覆盖物
      regionData: [], // 区域数据
    };
  },
  created() {},

  mounted() {
    this.getData("china");
    this.startDataMonitoring();
  },
  beforeDestroy() {
    // 清除定时器
    if (this.wsCheckTimer) {
      clearInterval(this.wsCheckTimer);
      this.wsCheckTimer = null;
    }
    // 清理地图资源
    this.clearEnterpriseMarkers();
  },
  methods: {
    // 地图准备就绪回调
    onMapReady(map) {
      this.map = map;
      console.log('地图初始化完成');

      // 添加九段线
      this.addNineLine();

      // 如果有区域数据，显示企业标记
      if (this.regionData.length > 0) {
        this.displayEnterpriseMarkers(this.regionData);
      }
    },

    // 地图点击事件
    onMapClick(event) {
      console.log('地图点击:', event);
      // 可以在这里添加点击交互逻辑
    },

    // 图层切换
    onLayerChange(layer) {
      this.currentLayer = layer;
      if (this.$refs.bigMap) {
        this.$refs.bigMap.switchLayer(layer);
      }
    },

    // 全屏切换
    onFullscreenChange(fullscreen) {
      console.log('全屏状态:', fullscreen);
      // 可以在这里处理全屏逻辑
    },

    // 添加九段线
    addNineLine() {
      if (this.map) {
        this.nineLineOverlays = createNineLineLayer(this.map);
      }
    },

    // 开始WebSocket数据监听
    startDataMonitoring() {
      this.wsCheckTimer = setInterval(() => {
        this.checkWebSocketData();
      }, 1000);
    },

    // 检查WebSocket数据
    checkWebSocketData() {
      if (dataModule.D0A02) {
        const newData = dataModule.D0A02;
        // 检查数据是否有变化
        if (newData.ship_num !== this.shipTotalCount || newData.enterprise_num !== this.enterpriseTotalCount) {
          this.shipTotalCount = newData.ship_num || 0;
          this.enterpriseTotalCount = newData.enterprise_num || 0;

          // 更新地图标题显示
          this.updateMapTitle();

          console.log('船舶数量数据更新:', {
            ship_num: this.shipTotalCount,
            enterprise_num: this.enterpriseTotalCount
          });
        }
      }
    },

    // 更新地图标题
    updateMapTitle() {
      this.maptitle = `企业数量：${this.enterpriseTotalCount} 家 | 接入船总数量：${this.shipTotalCount} 艘`;
    },

    getData(code) {
      currentGET("big8", { regionCode: code }).then((res) => {
        console.log("设备分布", res);
        if (res.success) {
          this.code = code;
          this.regionData = res.data.dataList || [];
          this.displayEnterpriseMarkers(this.regionData);
        } else {
          this.$Message.warning(res.msg);
        }
      });
    },

    // 显示企业标记
    displayEnterpriseMarkers(dataList) {
      if (!this.map || !dataList) return;

      // 清除现有标记
      this.clearEnterpriseMarkers();

      // 添加新标记
      dataList.forEach((item, index) => {
        // 这里需要根据实际数据结构调整
        // 假设数据包含 name, longitude, latitude, value 等字段
        if (item.longitude && item.latitude) {
          const marker = this.$refs.bigMap.addMarker(
            item.longitude,
            item.latitude,
            {
              title: item.name,
              content: `${item.name}: ${item.value || 0}个设备`,
              icon: this.getEnterpriseIcon(item.value)
            },
            `enterprise_${index}`
          );

          if (marker) {
            this.enterpriseMarkers.push(marker);
          }
        }
      });
    },

    // 获取企业图标
    getEnterpriseIcon(value) {
      // 根据设备数量返回不同大小的图标
      let size = 20;
      if (value > 100) size = 32;
      else if (value > 50) size = 28;
      else if (value > 10) size = 24;

      return {
        url: '/img/markers/enterprise.png',
        size: [size, size],
        anchor: [size / 2, size / 2]
      };
    },

    // 清除企业标记
    clearEnterpriseMarkers() {
      if (this.$refs.bigMap) {
        this.enterpriseMarkers.forEach(marker => {
          this.$refs.bigMap.removeMarker(marker);
        });
      }
      this.enterpriseMarkers = [];
    },

    // 简化的消息提示方法
    message(text) {
      this.$Message({
        text: text,
        type: "warning",
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.centermap {
  .maptitle {
    display: flex;
    justify-content: right;
    margin: 25px 0 5px 0;
    box-sizing: border-box;

    .titletext {
      font-size: 26px;
      font-weight: 300;
      letter-spacing: 1px;
      background: linear-gradient(
        92deg,
        #0072ff 0%,
        #00eaff 48.8525390625%,
        #01aaff 100%
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 0 16px;
    }

    .zuo,
    .you {
      background-size: 100% 100%;
      width: 26px;
      height: 16px;
      margin-top: 7px;
    }

    .zuo {
      background: url("../../assets/img/xiezuo.png") no-repeat;
    }

    .you {
      background: url("../../assets/img/xieyou.png") no-repeat;
    }
  }

  .mapwrap {
    height: 900px;
    width: 100%;
    box-sizing: border-box;
    position: relative;

    .big-map-container {
      width: 100%;
      height: 100%;
      position: relative;
    }
  }
}
</style>
